{
	"servers": {
		"sequential-thinking": {
			"id": "sequential-thinking",
			"name": "sequential-thinking",
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"-y",
					"@modelcontextprotocol/server-sequential-thinking"
				]
			}
		},
		"memory": {
			"id": "memory",
			"name": "memory",
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"-y",
					"@modelcontextprotocol/server-memory"
				]
			}
		},
		"filesystem": {
			"id": "filesystem",
			"name": "filesystem",
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"-y",
					"@modelcontextprotocol/server-filesystem",
					"%USERPROFILE%"
				]
			}
		},
		"powershell": {
			"id": "powershell",
			"name": "powershell",
			"config": {
				"type": "stdio",
				"command": "pwsh.exe",
				"args": [
					"-<PERSON><PERSON><PERSON>",
					"-<PERSON><PERSON><PERSON><PERSON><PERSON>",
					"-<PERSON>",
					"-"
				],
				"env": {}
			}
		},
		// "playwright": {
		// 	"id": "playwright",
		// 	"name": "playwright",
		// 	"gallery": true,
		// 	"config": {
		// 		"type": "stdio",
		// 		"command": "npx",
		// 		"args": [
		// 			"@playwright/mcp@latest"
		// 		]
		// 	}
		// },
		"github": {
			"id": "github",
			"name": "github",
			"gallery": true,
			"config": {
				"type": "http",
				"url": "https://api.githubcopilot.com/mcp/"
			}
		},
		"microsoft.docs.mcp": {
			"id": "microsoft.docs.mcp",
			"name": "microsoft.docs.mcp",
			"version": "0.0.1",
			"config": {
				"type": "http",
				"url": "https://learn.microsoft.com/api/mcp"
			}
		},
		"context7": {
			"id": "context7",
			"name": "context7",
			"gallery": true,
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"-y",
					"@upstash/context7-mcp@latest"
				]
			}
		},
		"gcloud": {
			"id": "gcloud",
			"name": "gcloud",
			"config": {
				"type": "stdio",
				"command": "gcloud",
				"args": [],
				"env": {}
			}
		},
		"firebase-cli": {
			"id": "firebase-cli",
			"name": "firebase-cli",
			"config": {
				"type": "stdio",
				"command": "firebase",
				"args": [],
				"env": {
					"FIREBASE_PROJECT": "agentasaservice-dev"
				}
			}
		},
		"git": {
			"id": "git",
			"name": "git",
			"config": {
				"type": "stdio",
				"command": "git.exe",
				"args": [],
				"env": {}
			}
		},
		"dotnet": {
			"id": "dotnet",
			"name": "dotnet",
			"config": {
				"type": "stdio",
				"command": "dotnet",
				"args": [],
				"env": {}
			}
		},
		"docker": {
			"id": "docker",
			"name": "docker",
			"config": {
				"type": "stdio",
				"command": "docker",
				"args": [],
				"env": {}
			}
		},
		"cloudflare-observability": {
			"id": "cloudflare-observability",
			"name": "cloudflare-observability",
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"mcp-remote",
					"https://observability.mcp.cloudflare.com/sse"
				]
			}
		},
		"cloudflare-bindings": {
			"id": "cloudflare-bindings",
			"name": "cloudflare-bindings",
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"mcp-remote",
					"https://bindings.mcp.cloudflare.com/sse"
				]
			}
		},
		"github-cli": {
			"id": "github-cli",
			"name": "github-cli",
			"config": {
				"type": "stdio",
				"command": "gh",
				"args": [],
				"env": {}
			}
		},
		"puppeteer": {
			"id": "puppeteer",
			"name": "puppeteer",
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"-y",
					"@modelcontextprotocol/server-puppeteer"
				],
				"env": {}
			}
		},
		"playwright": {
			"id": "playwright",
			"name": "playwright",
			"config": {
				"type": "stdio",
				"command": "npx",
				"args": [
					"-y",
					"@executeautomation/playwright-mcp-server"
				],
				"env": {}
			}
		},
		"web-browser": {
			"id": "web-browser",
			"name": "web-browser",
			"config": {
				"type": "stdio",
				"command": "uv",
				"args": [
					"tool",
					"run",
					"web-browser-mcp-server"
				]
			}
		}
	},
	"inputs": [
		{
			"id": "github_token",
			"description": "GitHub personal access token",
			"type": "promptString",
			"password": true
		}
	]
}