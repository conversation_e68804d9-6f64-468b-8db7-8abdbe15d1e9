{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AI": {"UseGpuAcceleration": true, "ModelPath": "", "ModelDownloadUrl": "https://huggingface.co/microsoft/Phi-3-mini-4k-instruct-onnx/resolve/main/cpu_and_mobile/cpu-int4-rtn-block-32-acc-level-4/phi3-mini-4k-instruct-cpu-int4-rtn-block-32-acc-level-4.onnx", "MaxTokens": 512, "Temperature": 0.7}, "KnowledgeBase": {"DatabasePath": "Data/knowledge.db", "EnableAutoBackup": true, "BackupIntervalHours": 24}}