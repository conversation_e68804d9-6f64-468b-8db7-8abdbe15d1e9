<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:SupportAssistant.Desktop.ViewModels"
        xmlns:conv="using:SupportAssistant.Desktop.Converters"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="900" d:DesignHeight="600"
        x:Class="SupportAssistant.Desktop.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"        
        Title="SupportAssistant"
        MinWidth="800" MinHeight="500">

    <Design.DataContext>
        <!-- This only sets the DataContext for the previewer in an IDE,
             to set the actual DataContext for runtime, set the DataContext property in code (look at App.axaml.cs) -->
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" Text="{Binding Greeting}" FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>

        <!-- Status Bar -->
        <Border Grid.Row="1" Background="LightBlue" CornerRadius="3" Padding="8,4" Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="Status: " FontWeight="SemiBold"/>
                <TextBlock Text="{Binding StatusMessage}"/>
                <TextBlock Text=" | Processing..." IsVisible="{Binding IsProcessing}" Foreground="Orange" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Chat Messages -->
        <ScrollViewer Grid.Row="2" Margin="0,0,0,10" Name="ChatScrollViewer">
            <ItemsControl ItemsSource="{Binding Messages}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Margin="0,2" Padding="10,8" CornerRadius="5">
                            <Border.Background>
                                <SolidColorBrush Color="{Binding Sender, Converter={x:Static conv:MessageColorConverter.Instance}}"/>
                            </Border.Background>
                            <StackPanel>
                                <TextBlock FontWeight="SemiBold" FontSize="12" Opacity="0.8">
                                    <TextBlock.Text>
                                        <MultiBinding StringFormat="{}{0} - {1:HH:mm:ss}">
                                            <Binding Path="Sender"/>
                                            <Binding Path="Timestamp"/>
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                                <TextBlock Text="{Binding Content}" TextWrapping="Wrap" Margin="0,4,0,0"/>
                            </StackPanel>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- User Input -->
        <TextBox Grid.Row="3" Text="{Binding UserInput}" Watermark="Type your question here..." 
                 Margin="0,0,0,10" AcceptsReturn="False">
            <TextBox.KeyBindings>
                <KeyBinding Gesture="Enter" Command="{Binding SendMessageCommand}"/>
            </TextBox.KeyBindings>
        </TextBox>

        <!-- Buttons -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" Spacing="10">
            <Button Content="Send" Command="{Binding SendMessageCommand}" 
                    IsEnabled="{Binding !IsProcessing}" MinWidth="80"/>
            <Button Content="Clear Chat" Command="{Binding ClearMessagesCommand}" 
                    IsEnabled="{Binding !IsProcessing}" MinWidth="80"/>
        </StackPanel>

        <!-- Help Text -->
        <TextBlock Grid.Row="5" Margin="0,10,0,0" FontSize="11" Opacity="0.7" TextWrapping="Wrap">
            <Run Text="💡 Tip: Press Enter to send your message. Ask about Windows troubleshooting, system issues, or technical problems."/>
        </TextBlock>
    </Grid>

</Window>