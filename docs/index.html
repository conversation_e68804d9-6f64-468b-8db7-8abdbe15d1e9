<!DOCTYPE html>
<html lang="en" class="scroll-smooth">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Analysis: On-Device vs. Cloud AI for FOSS Projects</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Chosen Palette: Subtle Tech -->
    <!-- Application Structure Plan: The SPA is structured as a narrative journey to guide a decision-maker. It starts with the final recommendation ("The Verdict") for quick insights, then allows for deep exploration through dedicated, interactive sections: 1. A side-by-side "Core Comparison" of Local vs. Cloud across key metrics (Cost, Privacy, etc.). 2. An interactive "Cost Simulator" to make the financial impact tangible. 3. A breakdown of the "Recommended Architecture" (RAG) using a clickable diagram. 4. An interactive "Technology Stack" explorer. 5. A step-by-step "Implementation Guide". 6. A new "AI-Powered Strategy" section that uses the Gemini API to generate a custom implementation plan based on the report's findings. This structure transforms the dense report into an active decision-support tool, prioritizing clarity and impact over the report's original linear format. The user can either get the summary instantly or follow the logical flow to understand the 'why' behind the recommendation. -->
    <!-- Visualization & Content Choices: 
        - Report Info: TCO Comparison -> Goal: Compare -> Viz: Interactive Tabs & Side-by-Side Cards -> Interaction: User clicks tabs (Cost, Privacy) to switch context -> Justification: Breaks down the complex comparison into manageable themes. -> Library/Method: HTML/Tailwind/JS.
        - Report Info: Cloud Cost Projection Table -> Goal: Show financial impact -> Viz: Interactive Line Chart with Sliders -> Interaction: User adjusts "Users" and "Queries" sliders; chart updates in real-time -> Justification: Viscerally demonstrates the unsustainable, exponential cost growth of cloud APIs, making it more impactful than a static table. -> Library/Method: Chart.js.
        - Report Info: RAG & Function Calling Workflows -> Goal: Explain a process -> Viz: Flowchart Diagram -> Interaction: Clicking on a step in the diagram reveals a detailed explanation. -> Justification: Simplifies complex technical processes into clear, explorable steps. -> Library/Method: HTML/Tailwind/JS.
        - Report Info: Windows AI Stack Components -> Goal: Organize information -> Viz: Grid of clickable cards -> Interaction: User clicks a technology card to view its description. -> Justification: Provides a high-level overview while allowing users to drill down into specific components of interest. -> Library/Method: HTML/Tailwind/JS.
        - Report Info: Key report findings (On-device, RAG, Phi-3) -> Goal: Synthesize & Action -> Viz: Dynamic Text Generation -> Interaction: User clicks a button to send a curated prompt to the Gemini API, which returns a structured implementation plan. -> Justification: Adds a powerful generative feature that turns the report's analysis into a personalized, actionable artifact for the user. -> Library/Method: Gemini API via fetch.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        .nav-link {
            transition: color 0.3s, border-bottom-color 0.3s;
        }

        .nav-link:hover {
            color: #0d9488;
            /* teal-600 */
        }

        .active-tab {
            background-color: #0d9488;
            /* teal-600 */
            color: white;
            border-color: #0d9488;
            /* teal-600 */
        }

        .inactive-tab {
            background-color: #f1f5f9;
            /* slate-100 */
            color: #475569;
            /* slate-600 */
            border-color: #cbd5e1;
            /* slate-300 */
        }

        .chart-container {
            position: relative;
            height: 300px;
            max-height: 40vh;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }

        .diagram-node {
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }

        .diagram-node:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        .diagram-arrow {
            color: #94a3b8;
            /* slate-400 */
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-in-out;
        }

        .gemini-loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0d9488;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body class="bg-slate-50 text-slate-700">

    <header class="bg-white/80 backdrop-blur-lg sticky top-0 z-50 border-b border-slate-200">
        <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <span class="font-bold text-xl text-teal-700">AI Strategy Analyzer</span>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#verdict" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600">The
                            Verdict</a>
                        <a href="#comparison"
                            class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600">Comparison</a>
                        <a href="#cost-simulator"
                            class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600">Cost Simulator</a>
                        <a href="#architecture"
                            class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600">Architecture</a>
                        <a href="#implementation"
                            class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600">Implementation</a>
                        <a href="#ai-strategy"
                            class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600">✨ AI Strategy</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section id="hero" class="bg-white pt-16 pb-20 text-center">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <h1 class="text-4xl md:text-5xl font-extrabold text-slate-900 tracking-tight">Architecting AI for
                    Open-Source</h1>
                <p class="mt-4 max-w-3xl mx-auto text-lg text-slate-600">An interactive analysis of Local vs. Cloud
                    inference for a sustainable, free Windows application.</p>
            </div>
        </section>

        <section id="verdict" class="py-16 sm:py-24 bg-teal-50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <p class="text-base font-semibold text-teal-600 uppercase tracking-wider">The Final Recommendation
                    </p>
                    <h2 class="mt-2 text-3xl font-extrabold text-slate-900 tracking-tight sm:text-4xl">On-Device
                        Inference is the Only Viable Path</h2>
                </div>
                <div class="mt-12 max-w-4xl mx-auto text-lg text-slate-600 text-center">
                    <p>After a comprehensive analysis of financial viability, user privacy, performance, and strategic
                        risk, the verdict is clear. The **on-device model** is the only financially sustainable and
                        strategically sound architecture for a free, open-source application. It aligns with the FOSS
                        cost structure, offers superior privacy and performance, and mitigates the significant risks
                        associated with third-party cloud APIs.</p>
                    <p class="mt-4">The cloud model's pay-per-query pricing creates a direct and unsustainable financial
                        burden that punishes success, making it fundamentally incompatible with a non-monetized project.
                        Explore the detailed comparison and cost simulator below to understand the data behind this
                        conclusion.</p>
                </div>
            </div>
        </section>

        <section id="comparison" class="py-16 sm:py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl font-extrabold text-slate-900 tracking-tight sm:text-4xl">Core Comparison: Local
                        vs. Cloud</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-xl text-slate-500">A head-to-head look at the critical
                        trade-offs.</p>
                </div>

                <div class="mt-12 max-w-5xl mx-auto">
                    <div class="mb-8 flex justify-center border-b border-slate-300">
                        <button data-tab="cost"
                            class="comparison-tab active-tab px-4 py-3 text-sm md:text-base font-medium border-b-2">💰
                            Cost & Viability</button>
                        <button data-tab="performance"
                            class="comparison-tab inactive-tab px-4 py-3 text-sm md:text-base font-medium border-b-2">⚡
                            Performance & UX</button>
                        <button data-tab="privacy"
                            class="comparison-tab inactive-tab px-4 py-3 text-sm md:text-base font-medium border-b-2">🛡️
                            Privacy & Security</button>
                        <button data-tab="risk"
                            class="comparison-tab inactive-tab px-4 py-3 text-sm md:text-base font-medium border-b-2">📈
                            Strategic Risk</button>
                    </div>

                    <div id="comparison-content" class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Local Column -->
                        <div class="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
                            <h3 class="text-2xl font-bold text-slate-800">On-Device (Local)</h3>
                            <div class="mt-4 space-y-4 text-slate-600">
                                <div id="local-cost" class="comparison-pane">
                                    <h4 class="font-semibold text-teal-700">++ Near-Zero Recurring Cost</h4>
                                    <p>The developer has a one-time development cost. Recurring operational costs
                                        (hardware, electricity) are externalized to the end-user as standard system
                                        requirements. This model is financially sustainable for FOSS.</p>
                                </div>
                                <div id="local-performance" class="comparison-pane hidden">
                                    <h4 class="font-semibold text-teal-700">++ Very Low Latency</h4>
                                    <p>Inference runs directly on the user's hardware, providing near-instantaneous
                                        response times. This results in a highly responsive and fluid user experience
                                        without any network lag.</p>
                                </div>
                                <div id="local-privacy" class="comparison-pane hidden">
                                    <h4 class="font-semibold text-teal-700">++ Maximum Privacy</h4>
                                    <p>All user data, prompts, and system information remain on the local device. No
                                        sensitive information is ever transmitted to a third party, eliminating privacy
                                        and data sovereignty concerns.</p>
                                </div>
                                <div id="local-risk" class="comparison-pane hidden">
                                    <h4 class="font-semibold text-teal-700">+ Low Strategic Risk</h4>
                                    <p>Aligns with the core strategy of the platform vendor (Microsoft) for on-device
                                        AI. The application is self-contained and not dependent on volatile third-party
                                        API pricing or availability.</p>
                                </div>
                            </div>
                        </div>
                        <!-- Cloud Column -->
                        <div class="bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
                            <h3 class="text-2xl font-bold text-slate-800">Cloud API</h3>
                            <div class="mt-4 space-y-4 text-slate-600">
                                <div id="cloud-cost" class="comparison-pane">
                                    <h4 class="font-semibold text-red-600">-- High Recurring Cost</h4>
                                    <p>The developer pays a per-query fee that scales directly with user growth. This
                                        creates an unsustainable financial burden that punishes success and is fatal for
                                        a non-monetized FOSS project.</p>
                                </div>
                                <div id="cloud-performance" class="comparison-pane hidden">
                                    <h4 class="font-semibold text-red-600">- High Latency</h4>
                                    <p>Every query requires a network round-trip to a remote server, introducing
                                        noticeable lag. The user experience can feel sluggish and is dependent on a
                                        stable internet connection.</p>
                                </div>
                                <div id="cloud-privacy" class="comparison-pane hidden">
                                    <h4 class="font-semibold text-red-600">-- Poor Privacy</h4>
                                    <p>User queries and potentially sensitive contextual data must be sent to a
                                        third-party service. This creates significant privacy, security, and potential
                                        compliance risks.</p>
                                </div>
                                <div id="cloud-risk" class="comparison-pane hidden">
                                    <h4 class="font-semibold text-red-600">-- High Strategic Risk</h4>
                                    <p>Creates dependency on a third-party's volatile, likely subsidized pricing. Vendor
                                        lock-in means any API changes, price hikes, or service deprecations could
                                        cripple the application.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="cost-simulator" class="py-16 sm:py-24 bg-slate-100">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl font-extrabold text-slate-900 tracking-tight sm:text-4xl">Interactive Cost
                        Simulator</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-xl text-slate-500">See for yourself why the cloud model is
                        unsustainable for a free app.</p>
                </div>

                <div class="mt-12 max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                        <div>
                            <div class="mb-6">
                                <label for="users-slider" class="block text-sm font-medium text-slate-700">Number of
                                    Active Users: <span id="users-value"
                                        class="font-bold text-teal-600">10,000</span></label>
                                <input id="users-slider" type="range" min="100" max="100000" value="10000" step="100"
                                    class="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer">
                            </div>
                            <div>
                                <label for="queries-slider" class="block text-sm font-medium text-slate-700">Average
                                    Queries per User/Day: <span id="queries-value"
                                        class="font-bold text-teal-600">5</span></label>
                                <input id="queries-slider" type="range" min="1" max="20" value="5" step="1"
                                    class="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer">
                            </div>
                        </div>
                        <div class="text-center bg-white p-6 rounded-lg shadow-lg border border-red-200">
                            <p class="text-lg text-slate-600">Developer's Estimated Monthly Bill:</p>
                            <p id="cost-output" class="text-4xl md:text-5xl font-bold text-red-600 mt-2">$787.50</p>
                        </div>
                    </div>
                </div>

                <div class="mt-12">
                    <div class="chart-container">
                        <canvas id="costChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <section id="architecture" class="py-16 sm:py-24 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl font-extrabold text-slate-900 tracking-tight sm:text-4xl">The Recommended
                        Architecture</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-xl text-slate-500">A look at the on-device RAG system and
                        agentic capabilities.</p>
                </div>

                <div class="mt-12">
                    <h3 class="text-2xl font-bold text-slate-800 text-center mb-2">Retrieval-Augmented Generation (RAG)
                        Flow</h3>
                    <p class="text-center text-slate-600 max-w-3xl mx-auto mb-8">This architecture ensures answers are
                        grounded in a trusted, local knowledge base, preventing AI "hallucination." Click on each step
                        to learn more.</p>
                    <div class="flex flex-col md:flex-row items-center justify-center gap-4 text-center">
                        <div data-info="rag-retrieval"
                            class="diagram-node bg-white p-4 rounded-lg shadow border border-slate-200 w-48">
                            <h4 class="font-bold text-teal-700">1. Retrieval</h4>
                            <p class="text-sm text-slate-500">Find relevant docs</p>
                        </div>
                        <div class="text-4xl diagram-arrow font-thin">→</div>
                        <div data-info="rag-augmentation"
                            class="diagram-node bg-white p-4 rounded-lg shadow border border-slate-200 w-48">
                            <h4 class="font-bold text-teal-700">2. Augmentation</h4>
                            <p class="text-sm text-slate-500">Combine query + docs</p>
                        </div>
                        <div class="text-4xl diagram-arrow font-thin">→</div>
                        <div data-info="rag-generation"
                            class="diagram-node bg-white p-4 rounded-lg shadow border border-slate-200 w-48">
                            <h4 class="font-bold text-teal-700">3. Generation</h4>
                            <p class="text-sm text-slate-500">Create grounded answer</p>
                        </div>
                    </div>
                    <div class="mt-6 max-w-2xl mx-auto p-4 bg-teal-50 rounded-lg border border-teal-200 transition-opacity duration-300 opacity-0"
                        id="rag-info-box">
                        <p id="rag-info-text" class="text-teal-800"></p>
                    </div>
                    <div id="rag-retrieval" class="hidden">When a user asks a question, the system first searches a
                        local, pre-processed knowledge base (built from trusted sources like Microsoft Learn and Stack
                        Overflow) to find the most relevant information snippets.</div>
                    <div id="rag-augmentation" class="hidden">The system combines the original user query with the
                        content of the retrieved documents into a single, comprehensive prompt that gives the AI model
                        all the context it needs.</div>
                    <div id="rag-generation" class="hidden">The augmented prompt is fed to the local Small Language
                        Model, which is instructed to generate an answer based *only* on the provided information,
                        ensuring accuracy and relevance.</div>
                </div>

                <div class="mt-16">
                    <h3 class="text-2xl font-bold text-slate-800 text-center mb-2">Technology Stack</h3>
                    <p class="text-center text-slate-600 max-w-3xl mx-auto mb-8">The on-device solution is powered by
                        Microsoft's modern Windows AI stack. Click each component to learn about its role.</p>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
                        <div data-info="tech-phi3"
                            class="diagram-node text-center bg-white p-6 rounded-lg shadow border border-slate-200">
                            <h4 class="font-bold text-lg text-teal-700">Phi-3 SLM</h4>
                            <p class="text-sm text-slate-500 mt-1">The AI Brain</p>
                        </div>
                        <div data-info="tech-onnx"
                            class="diagram-node text-center bg-white p-6 rounded-lg shadow border border-slate-200">
                            <h4 class="font-bold text-lg text-teal-700">ONNX Runtime</h4>
                            <p class="text-sm text-slate-500 mt-1">The Inference Engine</p>
                        </div>
                        <div data-info="tech-directml"
                            class="diagram-node text-center bg-white p-6 rounded-lg shadow border border-slate-200">
                            <h4 class="font-bold text-lg text-teal-700">DirectML</h4>
                            <p class="text-sm text-slate-500 mt-1">The Hardware Accelerator</p>
                        </div>
                        <div data-info="tech-winml"
                            class="diagram-node text-center bg-white p-6 rounded-lg shadow border border-slate-200">
                            <h4 class="font-bold text-lg text-teal-700">Windows ML</h4>
                            <p class="text-sm text-slate-500 mt-1">The OS Abstraction</p>
                        </div>
                    </div>
                    <div class="mt-6 max-w-3xl mx-auto p-4 bg-slate-100 rounded-lg border border-slate-200 transition-opacity duration-300 opacity-0"
                        id="tech-info-box">
                        <p id="tech-info-text" class="text-slate-700"></p>
                    </div>
                    <div id="tech-phi3" class="hidden">Microsoft's Phi-3 is a family of powerful Small Language Models
                        (SLMs) released under the permissive MIT License. They are optimized for on-device performance,
                        making them the ideal choice for this project.</div>
                    <div id="tech-onnx" class="hidden">The Open Neural Network Exchange (ONNX) Runtime is a
                        high-performance inference engine that loads and executes the AI model. Its cross-platform
                        nature and official support from Microsoft ensure robustness.</div>
                    <div id="tech-directml" class="hidden">DirectML is a DirectX 12-based API that provides hardware
                        acceleration. Its key benefit is providing a single, unified interface for GPUs and NPUs from
                        all major vendors (NVIDIA, AMD, Intel), solving the hardware fragmentation problem.</div>
                    <div id="tech-winml" class="hidden">Windows ML is a high-level API that simplifies development by
                        abstracting hardware, managing dependencies, and intelligently selecting the best processor
                        (CPU/GPU/NPU) for the AI task, reducing application complexity and size.</div>
                </div>
            </div>
        </section>

        <section id="implementation" class="py-16 sm:py-24 bg-slate-50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl font-extrabold text-slate-900 tracking-tight sm:text-4xl">Implementation Guide
                    </h2>
                    <p class="mt-4 max-w-2xl mx-auto text-xl text-slate-500">A step-by-step overview for building the
                        application in C# and Avalonia.</p>
                </div>
                <div class="mt-12 max-w-3xl mx-auto space-y-4">
                    <div class="accordion-item bg-white rounded-lg shadow-sm border border-slate-200">
                        <button
                            class="accordion-header w-full flex justify-between items-center text-left p-5 font-semibold text-slate-800">
                            <span>1. Project Setup & Dependencies</span>
                            <span class="accordion-icon text-teal-500 text-2xl font-thin">+</span>
                        </button>
                        <div class="accordion-content">
                            <div class="p-5 border-t border-slate-200 text-slate-600">
                                <p>Start by creating an Avalonia MVVM application. Install the
                                    `Microsoft.ML.OnnxRuntime.DirectML` NuGet package to get both GPU acceleration and a
                                    CPU fallback. Add your optimized `.onnx` model files to an `Assets` folder and
                                    ensure they are copied to the output directory.</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item bg-white rounded-lg shadow-sm border border-slate-200">
                        <button
                            class="accordion-header w-full flex justify-between items-center text-left p-5 font-semibold text-slate-800">
                            <span>2. Create an Inference Service</span>
                            <span class="accordion-icon text-teal-500 text-2xl font-thin">+</span>
                        </button>
                        <div class="accordion-content">
                            <div class="p-5 border-t border-slate-200 text-slate-600">
                                <p>Encapsulate all ONNX Runtime logic in a dedicated service class. Initialize the
                                    `InferenceSession` with `SessionOptions` to enable the DirectML execution provider.
                                    Expose an `async` method to run inference, which takes a prompt, handles
                                    tokenization, prepares input tensors, and decodes the output. Crucially, ensure the
                                    `InferenceSession` is properly disposed of to prevent memory leaks.</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item bg-white rounded-lg shadow-sm border border-slate-200">
                        <button
                            class="accordion-header w-full flex justify-between items-center text-left p-5 font-semibold text-slate-800">
                            <span>3. Build a Responsive UI</span>
                            <span class="accordion-icon text-teal-500 text-2xl font-thin">+</span>
                        </button>
                        <div class="accordion-content">
                            <div class="p-5 border-t border-slate-200 text-slate-600">
                                <p>In the `ViewModel`, use an `ObservableCollection` to store chat messages. Bind this
                                    to an `ItemsControl` in your View. Implement `async` command handlers that call the
                                    inference service. Use a boolean property (e.g., `IsBusy`) to show/hide loading
                                    indicators and disable input controls while the model is processing, ensuring the UI
                                    never freezes.</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item bg-white rounded-lg shadow-sm border border-slate-200">
                        <button
                            class="accordion-header w-full flex justify-between items-center text-left p-5 font-semibold text-slate-800">
                            <span>4. Handle Onboarding & Edge Cases</span>
                            <span class="accordion-icon text-teal-500 text-2xl font-thin">+</span>
                        </button>
                        <div class="accordion-content">
                            <div class="p-5 border-t border-slate-200 text-slate-600">
                                <p>On first launch, check for model files and provide a user-friendly download manager
                                    if they're missing. The initial, one-time model compilation can take minutes; run
                                    this on a background thread and show a clear notification to the user. Wrap all
                                    AI-related initializations in `try-catch` blocks to gracefully handle errors on
                                    systems with insufficient hardware.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="ai-strategy" class="py-16 sm:py-24 bg-teal-50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl font-extrabold text-slate-900 tracking-tight sm:text-4xl">✨ AI-Powered Strategy
                    </h2>
                    <p class="mt-4 max-w-2xl mx-auto text-xl text-slate-500">Turn this analysis into an actionable plan.
                    </p>
                </div>
                <div class="mt-12 max-w-3xl mx-auto text-center">
                    <p class="text-lg text-slate-600">Click the button below to use the Gemini API to generate a
                        high-level implementation plan based on the recommended on-device architecture. This will
                        provide a starting point for your project, outlining key phases and considerations.</p>
                    <button id="generate-plan-btn"
                        class="mt-8 inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-transform hover:scale-105">
                        Generate Implementation Plan
                    </button>
                </div>
                <div id="gemini-output-container"
                    class="mt-12 max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md border border-slate-200 hidden">
                    <div id="gemini-loader" class="mx-auto gemini-loader hidden"></div>
                    <div id="gemini-error" class="text-red-600 text-center hidden"></div>
                    <div id="gemini-content" class="prose prose-teal max-w-none"></div>
                </div>
            </div>
        </section>

    </main>

    <footer class="bg-slate-800 text-slate-400">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center text-sm">
            <p>Interactive report generated from source material on AI inference strategies.</p>
            <p class="mt-1">This is a conceptual web application and not a live product.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Comparison Tabs
            const comparisonTabs = document.querySelectorAll('.comparison-tab');
            const comparisonPanes = document.querySelectorAll('.comparison-pane');

            comparisonTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const target = tab.dataset.tab;

                    comparisonTabs.forEach(t => {
                        t.classList.remove('active-tab');
                        t.classList.add('inactive-tab');
                    });
                    tab.classList.add('active-tab');
                    tab.classList.remove('inactive-tab');

                    comparisonPanes.forEach(pane => {
                        if (pane.id.includes(target)) {
                            pane.classList.remove('hidden');
                        } else {
                            pane.classList.add('hidden');
                        }
                    });
                });
            });

            // Cost Simulator
            const usersSlider = document.getElementById('users-slider');
            const queriesSlider = document.getElementById('queries-slider');
            const usersValue = document.getElementById('users-value');
            const queriesValue = document.getElementById('queries-value');
            const costOutput = document.getElementById('cost-output');
            const ctx = document.getElementById('costChart').getContext('2d');

            const costPerQuery = 0.000525;

            let costChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Estimated Monthly Cloud Cost ($)',
                        data: [],
                        borderColor: '#dc2626', // red-600
                        backgroundColor: 'rgba(220, 38, 38, 0.1)',
                        fill: true,
                        tension: 0.4
                    }, {
                        label: 'On-Device Cost ($)',
                        data: [],
                        borderColor: '#0d9488', // teal-600
                        backgroundColor: 'rgba(13, 148, 136, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function (value, index, values) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Number of Active Users'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            function updateCost() {
                const users = parseInt(usersSlider.value);
                const queries = parseInt(queriesSlider.value);
                const monthlyQueries = users * queries * 30;
                const totalCost = monthlyQueries * costPerQuery;

                usersValue.textContent = users.toLocaleString();
                queriesValue.textContent = queries.toLocaleString();
                costOutput.textContent = totalCost.toLocaleString('en-US', { style: 'currency', currency: 'USD' });

                updateChart(users);
            }

            function updateChart(currentUserCount) {
                const maxUsers = parseInt(usersSlider.max);
                const step = maxUsers / 10;

                const labels = [];
                const cloudData = [];
                const localData = [];

                for (let i = 0; i <= maxUsers; i += step) {
                    if (i === 0 && maxUsers > 0) labels.push(100);
                    else labels.push(i);

                    const queries = parseInt(queriesSlider.value);
                    const monthlyQueries = i * queries * 30;
                    cloudData.push(monthlyQueries * costPerQuery);
                    localData.push(0); // On-device cost is effectively zero
                }

                costChart.data.labels = labels;
                costChart.data.datasets[0].data = cloudData;
                costChart.data.datasets[1].data = localData;
                costChart.update();
            }

            usersSlider.addEventListener('input', updateCost);
            queriesSlider.addEventListener('input', updateCost);
            updateCost();

            // Interactive Diagrams
            function setupInfoBox(nodeSelector, infoBoxId, infoTextId) {
                const nodes = document.querySelectorAll(nodeSelector);
                const infoBox = document.getElementById(infoBoxId);
                const infoText = document.getElementById(infoTextId);
                let activeNode = null;

                nodes.forEach(node => {
                    node.addEventListener('click', () => {
                        const infoContent = document.getElementById(node.dataset.info).textContent;

                        if (activeNode === node) {
                            infoBox.style.opacity = 0;
                            activeNode.classList.remove('ring-2', 'ring-teal-500');
                            activeNode = null;
                        } else {
                            if (activeNode) {
                                activeNode.classList.remove('ring-2', 'ring-teal-500');
                            }
                            infoText.textContent = infoContent;
                            infoBox.style.opacity = 1;
                            node.classList.add('ring-2', 'ring-teal-500');
                            activeNode = node;
                        }
                    });
                });
            }

            setupInfoBox('.diagram-node[data-info^="rag-"]', 'rag-info-box', 'rag-info-text');
            setupInfoBox('.diagram-node[data-info^="tech-"]', 'tech-info-box', 'tech-info-text');

            // Accordion
            const accordionItems = document.querySelectorAll('.accordion-item');
            accordionItems.forEach(item => {
                const header = item.querySelector('.accordion-header');
                const content = item.querySelector('.accordion-content');
                const icon = item.querySelector('.accordion-icon');

                header.addEventListener('click', () => {
                    const isOpen = content.style.maxHeight;

                    accordionItems.forEach(i => {
                        i.querySelector('.accordion-content').style.maxHeight = null;
                        i.querySelector('.accordion-icon').textContent = '+';
                    });

                    if (!isOpen) {
                        content.style.maxHeight = content.scrollHeight + 'px';
                        icon.textContent = '-';
                    }
                });
            });

            // Gemini API Integration
            const generateBtn = document.getElementById('generate-plan-btn');
            const outputContainer = document.getElementById('gemini-output-container');
            const loader = document.getElementById('gemini-loader');
            const errorContainer = document.getElementById('gemini-error');
            const contentContainer = document.getElementById('gemini-content');

            generateBtn.addEventListener('click', async () => {
                outputContainer.classList.remove('hidden');
                loader.classList.remove('hidden');
                contentContainer.classList.add('hidden');
                errorContainer.classList.add('hidden');
                generateBtn.disabled = true;
                generateBtn.classList.add('opacity-50', 'cursor-not-allowed');

                const prompt = `
                    Based on the following recommended strategy for a free, open-source Windows desktop application, generate a high-level implementation plan. The application's goal is to provide technical support by searching a local knowledge base.

                    **Core Strategy & Technology Stack:**
                    - **Inference Model:** On-Device (Local) to ensure zero recurring costs and maximum user privacy.
                    - **AI Model:** A Small Language Model (SLM) like Microsoft's Phi-3, optimized for on-device performance.
                    - **Architecture:** Retrieval-Augmented Generation (RAG) to ground responses in a trusted, local knowledge base and prevent hallucinations.
                    - **Runtime:** ONNX Runtime with the DirectML execution provider for hardware-accelerated inference across all major GPU vendors.
                    - **Framework:** C# with the Avalonia UI framework for a cross-platform desktop application.

                    Please generate a plan with the following sections, using Markdown for formatting:
                    1.  **Phase 1: Core Setup & Knowledge Base Preparation**
                    2.  **Phase 2: AI Service & RAG Implementation**
                    3.  **Phase 3: UI/UX & Application Integration**
                    4.  **Phase 4: Tooling & Agentic Capabilities** (for future features like modifying config files or registry keys)
                    5.  **Key Risks & Mitigations**

                    The tone should be professional, clear, and actionable for a development team.
                `;

                try {
                    let chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
                    const payload = { contents: chatHistory };
                    const apiKey = "";
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) {
                        throw new Error(`API request failed with status ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {

                        const text = result.candidates[0].content.parts[0].text;

                        // Basic Markdown to HTML conversion
                        let html = text
                            .replace(/(\*\*|__)(.*?)\1/g, '<strong>$2</strong>') // Bold
                            .replace(/(\*|_)(.*?)\1/g, '<em>$2</em>')       // Italic
                            .replace(/`([^`]+)`/g, '<code>$1</code>')       // Inline code
                            .replace(/### (.*$)/gim, '<h3>$1</h3>')         // H3
                            .replace(/## (.*$)/gim, '<h2>$1</h2>')           // H2
                            .replace(/\n\s*\*\s/g, '\n<ul>\n* ')              // Start of UL
                            .replace(/(\n\s*\*\s.*)+/g, '$&</ul>')            // End of UL
                            .replace(/\*\s(.*)/g, '<li>$1</li>')            // List items
                            .replace(/<\/ul>\n<ul>/g, '')                     // Fix nested lists
                            .replace(/\n/g, '<br>');                         // Newlines

                        contentContainer.innerHTML = html;
                        contentContainer.classList.remove('hidden');
                    } else {
                        throw new Error("Invalid response structure from API.");
                    }
                } catch (error) {
                    console.error("Gemini API Error:", error);
                    errorContainer.textContent = `An error occurred while generating the plan. Please check the console for details. Error: ${error.message}`;
                    errorContainer.classList.remove('hidden');
                } finally {
                    loader.classList.add('hidden');
                    generateBtn.disabled = false;
                    generateBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            });
        });
    </script>
</body>

</html>